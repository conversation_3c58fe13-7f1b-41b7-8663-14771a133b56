package com.workplat.push.task;

import com.alibaba.fastjson2.JSON;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.workplat.gss.script.dubbo.constants.ScriptConstants;
import com.workplat.gss.script.dubbo.loader.ScriptRunnerService;
import com.workplat.gss.service.item.dubbo.matter.constant.MatterPublicType;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatterPublic;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterPublicService;
import com.workplat.gss.service.item.dubbo.matter.vo.distribute.ConfMatterDistributeWorkflowVO;
import com.workplat.push.model.InstancePushLogQuery;
import com.workplat.push.entity.InstancePushLog;
import com.workplat.push.service.InstancePushLogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: InstancePushTask
 * @Description:
 * @Author: Yang Fan
 * @Date: 2025-07-10 15:23
 * @Version
 **/
@Component
//@Profile("prod")
public class InstancePushTask {

    private final ConfMatterPublicService confMatterPublicService;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final ScriptRunnerService scriptRunnerService;
    private final InstancePushLogService instancePushLogService;

    public InstancePushTask(ConfMatterPublicService confMatterPublicService,
                            BizInstanceInfoService bizInstanceInfoService,
                            ScriptRunnerService scriptRunnerService,
                            InstancePushLogService instancePushLogService) {
        this.confMatterPublicService = confMatterPublicService;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.scriptRunnerService = scriptRunnerService;
        this.instancePushLogService = instancePushLogService;
    }

    private static final Logger logger = LoggerFactory.getLogger(InstancePushTask.class);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 每5秒执行一次推送任务，确保前次任务完成后再开始新的任务
    @Scheduled(fixedDelay = 5000)
    public void pushTask() {
        logger.info("开始执行办件推送任务，当前时间：{}", LocalDateTime.now().format(formatter));
        
        try {
            // 执行推送逻辑
            push();
            logger.info("本次推送任务执行完毕");
        } catch (Exception e) {
            logger.error("推送任务执行过程中发生异常", e);
        }
    }

    /**
     * 推送核心逻辑：
     * 1. 查询待推送的办件（状态为"0"-未推送 或 "2"-推送失败）
     * 2. 对每个待推送办件执行推送逻辑
     * 3. 根据推送结果更新推送状态
     */
    public void push() {
        // 获取待推送列表
        InstancePushLogQuery query = new InstancePushLogQuery();
        // 查询待推送的办件（状态为"0"-未推送 或 "2"-推送失败）
        query.setPushStatus(List.of("0", "2"));
        // 设置查询当天零点作为创建时间的起始
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        query.setCreateTimeStart(java.util.Date.from(todayStart.atZone(java.time.ZoneId.systemDefault()).toInstant()));
        List<InstancePushLog> pushLogs = instancePushLogService.queryForList(query);
        logger.info("共查询到 {} 个待推送办件", pushLogs.size());
        
        for (InstancePushLog instancePushLog : pushLogs) {
            try {
                logger.info("开始推送办件，ID: {}", instancePushLog.getInstance().getId());
                
                // 执行推送逻辑
                ResponseData<Object> responseData = executeScript(instancePushLog.getInstance().getId());
                
                // 处理推送结果
                if (responseData.getCode() == 200) {
                    // 推送成功
                    instancePushLog.setPushStatus("1"); // 设置为"1"-推送成功
                    instancePushLog.setPushTime(new java.util.Date()); // 记录推送时间
                    instancePushLog.setPushResult("推送成功"); // 记录推送结果
                    logger.info("办件推送成功，ID: {}", instancePushLog.getInstance().getId());
                } else {
                    // 推送失败
                    instancePushLog.setPushResult("推送失败：" + responseData.getMessage()); // 记录失败原因
                    logger.warn("办件推送失败，ID: {}, 原因: {}", instancePushLog.getInstance().getId(), responseData.getMessage());
                }
                
                // 更新推送状态
                instancePushLogService.update(instancePushLog);
                
            } catch (Exception e) {
                logger.error("处理办件推送时发生异常，ID: {}", instancePushLog.getInstance().getId(), e);
            }
        }
    }

    private ResponseData<Object> executeScript(String instanceId) {
        try {
            BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
            // 获得分发流程配置
            ConfMatterDistributeWorkflowVO distributeWorkflowVO = null;
            ConfMatterPublic confMatterPublic = confMatterPublicService.queryById(bizInstanceInfo.getMatterPublicId());
            try {
                String json = confMatterPublicService.getAnyJson(confMatterPublic.getId(), MatterPublicType.WORK_FLOW_JSON);
                distributeWorkflowVO = JSON.parseObject(json, ConfMatterDistributeWorkflowVO.class);
            } catch (Exception e) {
                throw new BusinessException("获得分发流程配置失败：" + e.getMessage());
            }

            if (distributeWorkflowVO == null) {
                return ResponseData.error().message("未查找到分发流程配置").build();
            }

            // 脚本-办件推送脚本
            if (distributeWorkflowVO.isBindDistributionDriverScripts()
                    && StringUtils.isNotEmpty(distributeWorkflowVO.getDistributionDriverScripts())) {
                String driverScripts = distributeWorkflowVO.getDistributionDriverScripts();
                String[] scripts = driverScripts.split(",");
                List<DistributionDriverOutput> outputList = new ArrayList<>();
                DistributionDriverInput input = new DistributionDriverInput();
                input.setInstanceId(instanceId);
                for (String scriptId : scripts) {
                    DistributionDriverOutput output = scriptRunnerService.run(scriptId
                            , ScriptConstants.ScriptType.DISTRIBUTION_DRIVER.getMethod()
                            , DistributionDriverInput.class
                            , input);
                    if (output != null && !output.isSuccess()) {
                        return ResponseData.error().message(output.getMsg()).build();
                    }
                }
            }
        } catch (Exception e) {
            return ResponseData.error().message(e.getMessage()).build();
        }
        return ResponseData.success().build();
    }
}
