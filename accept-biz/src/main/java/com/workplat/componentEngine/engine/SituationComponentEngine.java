package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSONArray;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.api.api.BizInstanceSituationApi;
import com.workplat.gss.application.dubbo.dto.BizInstanceQuotaDto;
import com.workplat.gss.application.dubbo.vo.ConfMatterAcceptQuotaVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 情境组件引擎
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Slf4j
@Service
public class SituationComponentEngine extends AbstractComponentEngine {

    private final BizInstanceSituationApi bizInstanceSituationApi;

    // 定义该引擎支持的组件编码
    private static final String CODE = ComponentEngineCode.SITUATION;

    protected SituationComponentEngine(BizInstanceSituationApi bizInstanceSituationApi) {
        this.bizInstanceSituationApi = bizInstanceSituationApi;
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        //  调用API获取数据
        ResponseData<List<ConfMatterAcceptQuotaVO>> listResponseData =
                bizInstanceSituationApi.get(componentDataContext.getInstanceId());
        List<ConfMatterAcceptQuotaVO> data = listResponseData.getData();

        // 封装返回数据
        ComponentRunVO componentRunVO = new ComponentRunVO();
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(data)
                .build();
        //  设置返回数据
        componentRunVO.setRenderData(List.of(renderData));
        return componentRunVO;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 获取提交数据
        Object submitData = componentDataContext.getSubmitData();

        Collection<ComponentRunVO.RenderData> renderDataList =
                JSONArray.parseArray(submitData.toString(), ComponentRunVO.RenderData.class);
        ComponentRunVO.RenderData renderData = renderDataList.iterator().next();

        List<BizInstanceQuotaDto> bizInstanceQuotas =
                JSONArray.parseArray(renderData.getComponentInfo().toString(), BizInstanceQuotaDto.class);

        processNestedQuotas(bizInstanceQuotas, componentDataContext.getInstanceId());

        // 保存数据
        bizInstanceSituationApi.save(bizInstanceQuotas);
    }

    // 递归处理嵌套 quotas
    private void processNestedQuotas(List<BizInstanceQuotaDto> nestedQuotas, String instanceId) {
        if (nestedQuotas == null || nestedQuotas.isEmpty()) {
            return;
        }

        nestedQuotas.forEach(dto -> {
            // 设置实例ID
            dto.setInstanceId(instanceId);

            if (dto.getOptions() != null && !dto.getOptions().isEmpty()) {
                // 过滤 options，保留与 defaultOptionId 匹配的项
                dto.setOptions(dto.getOptions().stream()
                        .filter(option -> dto.getDefaultOptionId().contains(option.getOptId()))
                        .collect(Collectors.toList()));

                // 继续处理下一层嵌套
                dto.getOptions().forEach(option -> {
                    if (option.getQuotas() != null && !option.getQuotas().isEmpty()) {
                        processNestedQuotas(option.getQuotas(), instanceId);
                    }
                });
            }
        });
    }
}
